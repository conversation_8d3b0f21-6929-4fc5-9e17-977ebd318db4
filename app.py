import datetime
import os.path
from flask import Flask, jsonify, render_template
from flask_cors import CORS

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/calendar.readonly"]

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

def get_calendar_service():
    """Get authenticated Google Calendar service."""
    creds = None
    # The file token.json stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                "credentials.json", SCOPES
            )
            creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open("token.json", "w") as token:
            token.write(creds.to_json())

    return build("calendar", "v3", credentials=creds)

def format_location(location):
    """Format location based on the original logic."""
    if not location:
        return None

    # Check if location is a 2- or 3-digit number
    if location.isdigit():
        if len(location) == 3:
            return f"USQuad {location}"
        elif len(location) == 2:
            return f"VASC {location}"
    return location

def clean_event_title(title):
    """Clean event title by removing everything after ' - '."""
    # Remove everything after " - " from the title
    if " - " in title:
        return title.split(" - ")[0]

    return title

@app.route('/')
def index():
    """Serve the main calendar page."""
    return render_template('index.html')

@app.route('/api/events/today')
def get_today_events():
    """Get today's events from Google Calendar."""
    try:
        service = get_calendar_service()

        # Get today's date range
        today = datetime.date.today()
        start_of_day = datetime.datetime.combine(today, datetime.time.min).replace(tzinfo=datetime.timezone.utc)
        end_of_day = datetime.datetime.combine(today, datetime.time.max).replace(tzinfo=datetime.timezone.utc)

        # Call the Calendar API
        events_result = (
            service.events()
            .list(
                calendarId="primary",
                timeMin=start_of_day.isoformat(),
                timeMax=end_of_day.isoformat(),
                singleEvents=True,
                orderBy="startTime",
            )
            .execute()
        )
        events = events_result.get("items", [])

        # Format events for FullCalendar and remove duplicates
        formatted_events = []
        seen_events = set()  # Track unique events

        for event in events:
            start_time = event["start"].get("dateTime", event["start"].get("date"))
            end_time = event["end"].get("dateTime", event["end"].get("date"))
            raw_summary = event.get("summary", "(No Title)")
            raw_location = event.get("location")
            location = format_location(raw_location)
            # Clean the title by removing everything after " - "
            summary = clean_event_title(raw_summary)
            description = event.get("description", "")

            # Create a unique key for deduplication
            # Use start time, end time, summary, and location to identify duplicates
            unique_key = (start_time, end_time, summary, location)

            # Skip if we've already seen this exact event
            if unique_key in seen_events:
                continue

            seen_events.add(unique_key)

            # Create event object for FullCalendar
            formatted_event = {
                "id": event.get("id"),
                "title": summary,
                "start": start_time,
                "end": end_time,
                "extendedProps": {
                    "location": location,
                    "description": description
                }
            }

            # Add location to title if available
            if location:
                formatted_event["title"] = f"{summary} @ {location}"

            formatted_events.append(formatted_event)

        return jsonify(formatted_events)

    except HttpError as error:
        return jsonify({"error": f"An error occurred: {error}"}), 500
    except Exception as error:
        return jsonify({"error": f"An unexpected error occurred: {error}"}), 500

@app.route('/api/events')
def get_events():
    """Get events from Google Calendar for a broader date range."""
    try:
        service = get_calendar_service()

        # Get events from 30 days ago to 30 days in the future
        start_date = datetime.date.today() - datetime.timedelta(days=30)
        end_date = datetime.date.today() + datetime.timedelta(days=30)

        start_time = datetime.datetime.combine(start_date, datetime.time.min).replace(tzinfo=datetime.timezone.utc)
        end_time = datetime.datetime.combine(end_date, datetime.time.max).replace(tzinfo=datetime.timezone.utc)

        # Call the Calendar API
        events_result = (
            service.events()
            .list(
                calendarId="primary",
                timeMin=start_time.isoformat(),
                timeMax=end_time.isoformat(),
                maxResults=200,
                singleEvents=True,
                orderBy="startTime",
            )
            .execute()
        )
        events = events_result.get("items", [])

        # Format events for FullCalendar and remove duplicates
        formatted_events = []
        seen_events = set()  # Track unique events

        for event in events:
            start_time = event["start"].get("dateTime", event["start"].get("date"))
            end_time = event["end"].get("dateTime", event["end"].get("date"))
            raw_summary = event.get("summary", "(No Title)")
            raw_location = event.get("location")
            location = format_location(raw_location)
            # Clean the title by removing everything after " - "
            summary = clean_event_title(raw_summary)
            description = event.get("description", "")

            # Create a unique key for deduplication
            # Use start time, end time, summary, and location to identify duplicates
            unique_key = (start_time, end_time, summary, location)

            # Skip if we've already seen this exact event
            if unique_key in seen_events:
                continue

            seen_events.add(unique_key)

            # Create event object for FullCalendar
            formatted_event = {
                "id": event.get("id"),
                "title": summary,
                "start": start_time,
                "end": end_time,
                "extendedProps": {
                    "location": location,
                    "description": description
                }
            }

            # Add location to title if available
            if location:
                formatted_event["title"] = f"{summary} @ {location}"

            formatted_events.append(formatted_event)

        return jsonify(formatted_events)

    except HttpError as error:
        return jsonify({"error": f"An error occurred: {error}"}), 500
    except Exception as error:
        return jsonify({"error": f"An unexpected error occurred: {error}"}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5001)
