<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today's Classes - Calendar View</title>

    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .view-toggle {
            text-align: center;
            margin-bottom: 20px;
        }

        .view-toggle button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .view-toggle button:hover {
            background-color: #0056b3;
        }

        .view-toggle button.active {
            background-color: #28a745;
        }

        #calendar {
            margin-top: 20px;
        }

        /* Custom FullCalendar styling */
        .fc-event {
            border-radius: 5px;
            border: none;
            padding: 2px 5px;
        }

        .fc-event-title {
            font-weight: 600;
        }

        .fc-daygrid-event {
            margin-bottom: 2px;
        }

        .fc-timegrid-event {
            border-radius: 3px;
        }

        /* Today's date highlighting */
        .fc-day-today {
            background-color: #fff3cd !important;
        }

        /* Event colors for different types */
        .fc-event.class-event {
            background-color: #007bff;
            border-color: #0056b3;
        }

        .fc-event.meeting-event {
            background-color: #28a745;
            border-color: #1e7e34;
        }

        .fc-event.other-event {
            background-color: #6c757d;
            border-color: #545b62;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin: 20px 0;
        }

        .info-panel {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .today-date {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Today's Classes</h1>

        <div class="info-panel">
            <div class="today-date" id="todayDate"></div>
        </div>

        <div class="view-toggle">
            <button id="todayBtn" class="active">Today's Classes</button>
            <button id="weekBtn">Week View</button>
            <button id="monthBtn">Month View</button>
        </div>

        <div id="loading" class="loading">Loading your classes...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="calendar"></div>
    </div>

    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const todayDateEl = document.getElementById('todayDate');

            // Set today's date
            const today = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            todayDateEl.textContent = today.toLocaleDateString('en-US', options);

            // Initialize calendar
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridDay',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridDay,timeGridWeek,dayGridMonth'
                },
                height: 'auto',
                slotMinTime: '06:00:00',
                slotMaxTime: '22:00:00',
                allDaySlot: false,
                nowIndicator: true,
                eventDisplay: 'block',
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                events: {
                    url: '/api/events/today',
                    failure: function(error) {
                        console.error('Error fetching events:', error);
                        loadingEl.style.display = 'none';
                        errorEl.textContent = `Error loading events: ${error.message}`;
                        errorEl.style.display = 'block';
                    }
                },
                eventDataTransform: function(eventData) {
                    // Add event classification for styling
                    let className = 'other-event';
                    const title = eventData.title.toLowerCase();

                    if (title.includes('class') || title.includes('lecture') ||
                        title.includes('lab') || title.includes('seminar') ||
                        title.includes('tutorial') || title.includes('workshop')) {
                        className = 'class-event';
                    } else if (title.includes('meeting') || title.includes('office hours')) {
                        className = 'meeting-event';
                    }

                    return {
                        ...eventData,
                        className: className
                    };
                },
                eventsSet: function(events) {
                    // Hide loading when events are loaded
                    loadingEl.style.display = 'none';
                    errorEl.style.display = 'none';
                },
                eventClick: function(info) {
                    const event = info.event;
                    const location = event.extendedProps.location;
                    const description = event.extendedProps.description;

                    let details = `📅 ${event.title}\n`;
                    details += `🕐 ${event.start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    if (event.end) {
                        details += ` - ${event.end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    }
                    if (location) {
                        details += `\n📍 ${location}`;
                    }
                    if (description) {
                        details += `\n📝 ${description}`;
                    }

                    alert(details);
                }
            });

            calendar.render();

            // View toggle buttons
            document.getElementById('todayBtn').addEventListener('click', function() {
                calendar.changeView('timeGridDay');
                calendar.gotoDate(new Date());
                updateActiveButton('todayBtn');
                // Update event source to today's events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events/today');
            });

            document.getElementById('weekBtn').addEventListener('click', function() {
                calendar.changeView('timeGridWeek');
                updateActiveButton('weekBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            document.getElementById('monthBtn').addEventListener('click', function() {
                calendar.changeView('dayGridMonth');
                updateActiveButton('monthBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            function updateActiveButton(activeId) {
                document.querySelectorAll('.view-toggle button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById(activeId).classList.add('active');
            }
        });
    </script>
</body>
</html>
