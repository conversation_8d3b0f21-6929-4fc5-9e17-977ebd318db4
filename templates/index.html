<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today's Classes - Calendar View</title>

    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.8em;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .info-panel {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .today-date {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .view-toggle {
            text-align: center;
            margin-bottom: 25px;
        }

        .view-toggle button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .view-toggle button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .view-toggle button.active {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
        }

        #calendar {
            margin-top: 25px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* FullCalendar Customization */
        .fc {
            font-family: inherit;
        }

        .fc-header-toolbar {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #dee2e6;
        }

        .fc-button-primary {
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
            border: none !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            padding: 8px 16px !important;
            transition: all 0.3s ease !important;
        }

        .fc-button-primary:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        }

        .fc-event {
            border: none !important;
            border-radius: 8px !important;
            padding: 4px 8px !important;
            font-weight: 600 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
        }

        .fc-event:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
        }

        .fc-event.class-event {
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
        }

        .fc-event.meeting-event {
            background: linear-gradient(135deg, #f093fb, #f5576c) !important;
        }

        .fc-event.other-event {
            background: linear-gradient(135deg, #a8edea, #fed6e3) !important;
            color: #2c3e50 !important;
        }

        .fc-day-today {
            background-color: rgba(240, 147, 251, 0.1) !important;
        }

        .fc-timegrid-slot {
            border-color: #f8f9fa !important;
        }

        .fc-timegrid-axis {
            background: #f8f9fa !important;
        }

        .loading {
            text-align: center;
            padding: 60px;
            font-size: 18px;
            color: #667eea;
            font-weight: 600;
        }

        .error {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #721c24;
            border-radius: 12px;
            margin: 20px 0;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Today's Classes</h1>

        <div class="info-panel">
            <p class="today-date" id="todayDate"></p>
        </div>

        <div class="view-toggle">
            <button id="todayBtn" class="active">📅 Today</button>
            <button id="weekBtn">📊 Week</button>
            <button id="monthBtn">🗓️ Month</button>
        </div>

        <div id="loading" class="loading">✨ Loading your classes...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="calendar"></div>
    </div>

    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const todayDateEl = document.getElementById('todayDate');

            // Set today's date
            const today = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            todayDateEl.textContent = today.toLocaleDateString('en-US', options);

            // Initialize calendar
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridDay',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridDay,timeGridWeek,dayGridMonth'
                },
                height: 700,
                slotMinTime: '07:00:00',
                slotMaxTime: '20:00:00',
                slotDuration: '00:30:00',
                allDaySlot: false,
                nowIndicator: true,
                eventDisplay: 'block',
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                dayHeaderFormat: {
                    weekday: 'long',
                    month: 'numeric',
                    day: 'numeric'
                },
                events: {
                    url: '/api/events/today',
                    failure: function(error) {
                        console.error('Error fetching events:', error);
                        loadingEl.style.display = 'none';
                        errorEl.textContent = `Error loading events: ${error.message}`;
                        errorEl.style.display = 'block';
                    }
                },
                eventDataTransform: function(eventData) {
                    // Add event classification for styling
                    let className = 'other-event';
                    const title = eventData.title.toLowerCase();

                    if (title.includes('class') || title.includes('lecture') ||
                        title.includes('lab') || title.includes('seminar') ||
                        title.includes('tutorial') || title.includes('workshop')) {
                        className = 'class-event';
                    } else if (title.includes('meeting') || title.includes('office hours')) {
                        className = 'meeting-event';
                    }

                    return {
                        ...eventData,
                        className: className
                    };
                },
                eventsSet: function(events) {
                    // Hide loading when events are loaded
                    loadingEl.style.display = 'none';
                    errorEl.style.display = 'none';
                },
                eventClick: function(info) {
                    const event = info.event;
                    const location = event.extendedProps.location;
                    const description = event.extendedProps.description;

                    let details = `📅 ${event.title}\n`;
                    details += `🕐 ${event.start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    if (event.end) {
                        details += ` - ${event.end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    }
                    if (location) {
                        details += `\n📍 ${location}`;
                    }
                    if (description) {
                        details += `\n📝 ${description}`;
                    }

                    alert(details);
                }
            });

            calendar.render();

            // View toggle buttons
            document.getElementById('todayBtn').addEventListener('click', function() {
                calendar.changeView('timeGridDay');
                calendar.gotoDate(new Date());
                updateActiveButton('todayBtn');
                // Update event source to today's events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events/today');
            });

            document.getElementById('weekBtn').addEventListener('click', function() {
                calendar.changeView('timeGridWeek');
                updateActiveButton('weekBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            document.getElementById('monthBtn').addEventListener('click', function() {
                calendar.changeView('dayGridMonth');
                updateActiveButton('monthBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            function updateActiveButton(activeId) {
                document.querySelectorAll('.view-toggle button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById(activeId).classList.add('active');
            }
        });
    </script>
</body>
</html>
