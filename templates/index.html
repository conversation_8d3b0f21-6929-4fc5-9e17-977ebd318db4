<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today's Classes - Calendar View</title>

    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 30px;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 600;
        }

        .info-panel {
            background: #e3f2fd;
            color: #1565c0;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 25px;
            text-align: center;
            border-left: 4px solid #2196f3;
        }

        .today-date {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .view-toggle {
            text-align: center;
            margin-bottom: 25px;
        }

        .view-toggle button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 6px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .view-toggle button:hover {
            background: #1976d2;
            transform: translateY(-1px);
        }

        .view-toggle button.active {
            background: #1565c0;
            box-shadow: 0 2px 8px rgba(21, 101, 192, 0.3);
        }

        #calendar {
            margin-top: 25px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e0e0e0;
        }

        /* FullCalendar Customization */
        .fc {
            font-family: inherit;
        }

        .fc-header-toolbar {
            padding: 16px 20px;
            background: #fafafa;
            border-bottom: 1px solid #e0e0e0;
        }

        .fc-button-primary {
            background: #2196f3 !important;
            border: 1px solid #1976d2 !important;
            border-radius: 4px !important;
            font-weight: 500 !important;
            padding: 6px 12px !important;
            transition: all 0.2s ease !important;
        }

        .fc-button-primary:hover {
            background: #1976d2 !important;
            border-color: #1565c0 !important;
        }

        .fc-event {
            border: none !important;
            border-radius: 4px !important;
            padding: 2px 6px !important;
            font-weight: 500 !important;
            font-size: 13px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            transition: all 0.2s ease !important;
        }

        .fc-event:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        }

        .fc-event.class-event {
            background: #2196f3 !important;
            border-left: 3px solid #1565c0 !important;
        }

        .fc-event.meeting-event {
            background: #4caf50 !important;
            border-left: 3px solid #2e7d32 !important;
        }

        .fc-event.other-event {
            background: #757575 !important;
            border-left: 3px solid #424242 !important;
        }

        .fc-day-today {
            background-color: #f3f8ff !important;
        }

        /* Time Grid Improvements */
        .fc-timegrid-slot {
            border-color: #f0f0f0 !important;
            height: 40px !important;
        }

        .fc-timegrid-slot-minor {
            border-color: #f8f8f8 !important;
        }

        .fc-timegrid-axis {
            background: #fafafa !important;
            border-right: 1px solid #e0e0e0 !important;
            width: 80px !important;
        }

        .fc-timegrid-axis-cushion {
            padding: 0 8px !important;
            font-size: 12px !important;
            color: #666 !important;
            font-weight: 500 !important;
        }

        .fc-timegrid-slot-label-cushion {
            font-size: 12px !important;
            color: #666 !important;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        .error {
            text-align: center;
            padding: 16px;
            background: #ffebee;
            color: #c62828;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Today's Classes</h1>

        <div class="info-panel">
            <p class="today-date" id="todayDate"></p>
        </div>

        <div class="view-toggle">
            <button id="todayBtn" class="active">📅 Today</button>
            <button id="weekBtn">📊 Week</button>
            <button id="monthBtn">🗓️ Month</button>
        </div>

        <div id="loading" class="loading">✨ Loading your classes...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="calendar"></div>
    </div>

    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.19/index.global.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const todayDateEl = document.getElementById('todayDate');

            // Set today's date
            const today = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            todayDateEl.textContent = today.toLocaleDateString('en-US', options);

            // Initialize calendar
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridDay',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridDay,timeGridWeek,dayGridMonth'
                },
                height: 650,
                slotMinTime: '07:00:00',
                slotMaxTime: '20:00:00',
                slotDuration: '01:00:00',
                slotLabelInterval: '01:00:00',
                allDaySlot: false,
                nowIndicator: true,
                eventDisplay: 'block',
                slotLabelFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                dayHeaderFormat: {
                    weekday: 'long',
                    month: 'numeric',
                    day: 'numeric'
                },
                events: {
                    url: '/api/events/today',
                    failure: function(error) {
                        console.error('Error fetching events:', error);
                        loadingEl.style.display = 'none';
                        errorEl.textContent = `Error loading events: ${error.message}`;
                        errorEl.style.display = 'block';
                    }
                },
                eventDataTransform: function(eventData) {
                    // Add event classification for styling
                    let className = 'other-event';
                    const title = eventData.title.toLowerCase();

                    if (title.includes('class') || title.includes('lecture') ||
                        title.includes('lab') || title.includes('seminar') ||
                        title.includes('tutorial') || title.includes('workshop')) {
                        className = 'class-event';
                    } else if (title.includes('meeting') || title.includes('office hours')) {
                        className = 'meeting-event';
                    }

                    return {
                        ...eventData,
                        className: className
                    };
                },
                eventsSet: function(events) {
                    // Hide loading when events are loaded
                    loadingEl.style.display = 'none';
                    errorEl.style.display = 'none';
                },
                eventClick: function(info) {
                    const event = info.event;
                    const location = event.extendedProps.location;
                    const description = event.extendedProps.description;

                    let details = `📅 ${event.title}\n`;
                    details += `🕐 ${event.start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    if (event.end) {
                        details += ` - ${event.end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                    }
                    if (location) {
                        details += `\n📍 ${location}`;
                    }
                    if (description) {
                        details += `\n📝 ${description}`;
                    }

                    alert(details);
                }
            });

            calendar.render();

            // View toggle buttons
            document.getElementById('todayBtn').addEventListener('click', function() {
                calendar.changeView('timeGridDay');
                calendar.gotoDate(new Date());
                updateActiveButton('todayBtn');
                // Update event source to today's events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events/today');
            });

            document.getElementById('weekBtn').addEventListener('click', function() {
                calendar.changeView('timeGridWeek');
                updateActiveButton('weekBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            document.getElementById('monthBtn').addEventListener('click', function() {
                calendar.changeView('dayGridMonth');
                updateActiveButton('monthBtn');
                // Update event source to all events
                calendar.getEventSources().forEach(source => source.remove());
                calendar.addEventSource('/api/events');
            });

            function updateActiveButton(activeId) {
                document.querySelectorAll('.view-toggle button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById(activeId).classList.add('active');
            }
        });
    </script>
</body>
</html>
