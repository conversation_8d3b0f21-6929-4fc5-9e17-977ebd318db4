# Class Calendar Web App

A web application that displays your Google Calendar events (especially classes) using FullCalendar in a beautiful, interactive interface.

## Features

- 📅 **Today's Classes View**: Focus on today's schedule with a clean time-grid layout
- 📊 **Multiple Views**: Switch between day, week, and month views
- 🎨 **Smart Event Styling**: Automatically categorizes and colors events (classes, meetings, etc.)
- 📱 **Responsive Design**: Works great on desktop and mobile devices
- 🔍 **Event Details**: Click on any event to see full details including location and description
- 🏫 **Location Formatting**: Automatically formats room numbers (e.g., "123" → "USQuad 123")

## Setup Instructions

### 1. Prerequisites

- Python 3.7 or higher
- Google Calendar API credentials

### 2. Google Calendar API Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Calendar API
4. Create credentials (OAuth 2.0 Client ID) for a desktop application
5. Download the credentials file and save it as `credentials.json` in this directory

### 3. Installation

1. Clone or download this project
2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### 4. Running the Application

1. Make sure you have `credentials.json` in the project directory
2. Run the Flask application:
   ```bash
   python app.py
   ```
3. Open your browser and go to `http://localhost:5000`
4. On first run, you'll be redirected to Google's authorization page to grant calendar access
5. After authorization, you'll see your calendar events displayed beautifully!

## File Structure

```
├── app.py                 # Flask backend server
├── quickstart.py         # Original Google Calendar script
├── requirements.txt      # Python dependencies
├── credentials.json      # Google API credentials (you need to add this)
├── token.json           # Auto-generated after first authorization
├── templates/
│   └── index.html       # Main web interface with FullCalendar
└── README.md           # This file
```

## API Endpoints

- `GET /` - Main calendar interface
- `GET /api/events/today` - Get today's events in JSON format
- `GET /api/events` - Get upcoming events in JSON format

## Customization

### Event Categories
The app automatically categorizes events based on keywords in the title:
- **Class Events** (blue): Contains "class", "lecture", "lab", "seminar", "tutorial", "workshop"
- **Meeting Events** (green): Contains "meeting", "office hours"
- **Other Events** (gray): Everything else

### Styling
You can customize the appearance by modifying the CSS in `templates/index.html`.

### Time Range
The calendar shows events from 6 AM to 10 PM by default. You can modify this in the FullCalendar configuration.

## Troubleshooting

1. **"No module named 'google'"**: Make sure you've installed the requirements with `pip install -r requirements.txt`
2. **"credentials.json not found"**: Download your Google API credentials and save them as `credentials.json`
3. **Authorization issues**: Delete `token.json` and restart the app to re-authorize
4. **No events showing**: Check that your Google Calendar has events and that the API has proper permissions

## Original Script

The original `quickstart.py` script is preserved and can still be run independently to see events in the terminal:
```bash
python quickstart.py
```
